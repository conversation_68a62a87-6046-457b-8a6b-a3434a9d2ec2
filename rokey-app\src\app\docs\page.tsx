'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  DocumentTextIcon,
  CodeBracketIcon,
  CogIcon,
  BoltIcon,
  KeyIcon,
  ChevronRightIcon,
  ClipboardDocumentIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  SparklesIcon,
  ArrowTopRightOnSquareIcon,
  CircleStackIcon,
  ListBulletIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';

interface CodeBlockProps {
  children: string;
  language?: string;
  title?: string;
}

function CodeBlock({ children, language = 'javascript', title }: CodeBlockProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(children);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Enhanced syntax highlighting for different languages
  const highlightSyntax = (code: string, lang: string) => {
    const lines = code.split('\n');
    return lines.map((line, index) => {
      let highlightedLine = line;
      
      if (lang === 'javascript' || lang === 'typescript') {
        // Keywords
        highlightedLine = highlightedLine.replace(
          /\b(const|let|var|function|async|await|import|export|from|return|if|else|for|while|try|catch|throw|new)\b/g,
          '<span class="text-purple-400">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-green-400">$1$2$1</span>'
        );
        // Comments
        highlightedLine = highlightedLine.replace(
          /(\/\/.*$)/g,
          '<span class="text-gray-500">$1</span>'
        );
        // Numbers
        highlightedLine = highlightedLine.replace(
          /\b(\d+\.?\d*)\b/g,
          '<span class="text-yellow-400">$1</span>'
        );
      } else if (lang === 'python') {
        // Python keywords
        highlightedLine = highlightedLine.replace(
          /\b(import|from|def|class|if|else|elif|for|while|try|except|with|as|return|yield|lambda|and|or|not|in|is|None|True|False)\b/g,
          '<span class="text-purple-400">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-green-400">$1$2$1</span>'
        );
        // Comments
        highlightedLine = highlightedLine.replace(
          /(#.*$)/g,
          '<span class="text-gray-500">$1</span>'
        );
      } else if (lang === 'bash' || lang === 'shell') {
        // Bash commands
        highlightedLine = highlightedLine.replace(
          /\b(curl|echo|export|cd|ls|mkdir|rm|cp|mv|grep|awk|sed)\b/g,
          '<span class="text-blue-400">$1</span>'
        );
        // Flags
        highlightedLine = highlightedLine.replace(
          /(-[a-zA-Z]+)/g,
          '<span class="text-yellow-400">$1</span>'
        );
        // Strings
        highlightedLine = highlightedLine.replace(
          /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          '<span class="text-green-400">$1$2$1</span>'
        );
      } else if (lang === 'json') {
        // JSON keys
        highlightedLine = highlightedLine.replace(
          /"([^"]+)":/g,
          '<span class="text-blue-400">"$1"</span>:'
        );
        // JSON strings
        highlightedLine = highlightedLine.replace(
          /:\s*"([^"]*)"/g,
          ': <span class="text-green-400">"$1"</span>'
        );
        // JSON numbers
        highlightedLine = highlightedLine.replace(
          /:\s*(\d+\.?\d*)/g,
          ': <span class="text-yellow-400">$1</span>'
        );
        // JSON booleans
        highlightedLine = highlightedLine.replace(
          /:\s*(true|false|null)/g,
          ': <span class="text-purple-400">$1</span>'
        );
      }

      return (
        <div key={index} className="table-row">
          <span className="table-cell text-gray-500 text-right pr-4 select-none w-8">
            {index + 1}
          </span>
          <span 
            className="table-cell text-gray-100"
            dangerouslySetInnerHTML={{ __html: highlightedLine || ' ' }}
          />
        </div>
      );
    });
  };

  return (
    <div className="relative bg-gray-900 rounded-xl overflow-hidden border border-gray-700 shadow-2xl">
      {title && (
        <div className="bg-gray-800 px-4 py-3 text-sm text-gray-300 border-b border-gray-700 flex items-center justify-between">
          <span className="font-medium">{title}</span>
          <span className="text-xs text-gray-500 uppercase tracking-wide">{language}</span>
        </div>
      )}
      <div className="relative">
        <pre className="p-4 overflow-x-auto text-sm font-mono leading-relaxed">
          <code className="table w-full">
            {highlightSyntax(children, language || 'javascript')}
          </code>
        </pre>
        <button
          onClick={handleCopy}
          className="absolute top-3 right-3 p-2 bg-gray-800/80 hover:bg-gray-700 rounded-lg transition-all duration-200 backdrop-blur-sm border border-gray-600"
          title="Copy to clipboard"
        >
          {copied ? (
            <CheckIcon className="h-4 w-4 text-green-400" />
          ) : (
            <ClipboardDocumentIcon className="h-4 w-4 text-gray-400" />
          )}
        </button>
      </div>
    </div>
  );
}

interface AlertProps {
  type: 'info' | 'warning' | 'tip';
  children: React.ReactNode;
}

function Alert({ type, children }: AlertProps) {
  const styles = {
    info: 'bg-blue-900/20 border-blue-500/30 text-blue-200',
    warning: 'bg-yellow-900/20 border-yellow-500/30 text-yellow-200',
    tip: 'bg-green-900/20 border-green-500/30 text-green-200'
  };

  const icons = {
    info: InformationCircleIcon,
    warning: ExclamationTriangleIcon,
    tip: SparklesIcon
  };

  const Icon = icons[type];

  return (
    <div className={`border rounded-xl p-4 ${styles[type]} backdrop-blur-sm`}>
      <div className="flex items-start gap-3">
        <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
        <div className="text-sm">{children}</div>
      </div>
    </div>
  );
}

export default function DocsPage() {
  const [activeSection, setActiveSection] = useState('getting-started');

  const sections = [
    { id: 'getting-started', title: 'Getting Started', icon: DocumentTextIcon },
    { id: 'authentication', title: 'Authentication', icon: KeyIcon },
    { id: 'api-reference', title: 'API Reference', icon: CodeBracketIcon },
    { id: 'routing-strategies', title: 'Routing Strategies', icon: BoltIcon },
    { id: 'configuration', title: 'Configuration', icon: CogIcon },
    { id: 'examples', title: 'Examples', icon: SparklesIcon },
    { id: 'sdks', title: 'SDKs & Libraries', icon: CircleStackIcon },
    { id: 'limits', title: 'Limits & Pricing', icon: ListBulletIcon },
  ];

  return (
    <div className="min-h-screen bg-black">
      <LandingNavbar />
      
      <div className="flex">
        {/* Sidebar */}
        <div className="w-80 flex-shrink-0 bg-gray-900 border-r border-gray-800 min-h-screen">
          <div className="sticky top-0 p-6">
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-white mb-2">RouKey Documentation</h1>
              <p className="text-gray-400 text-sm">
                Complete guide to integrating and using RouKey's intelligent AI gateway
              </p>
            </div>
            
            <nav className="space-y-2">
              {sections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center gap-3 px-4 py-3 text-sm rounded-xl transition-all duration-200 ${
                      activeSection === section.id
                        ? 'bg-orange-600 text-white shadow-lg shadow-orange-600/25'
                        : 'text-gray-300 hover:text-white hover:bg-gray-800'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    {section.title}
                  </button>
                );
              })}
            </nav>
            
            <div className="mt-8 p-4 bg-gray-800 rounded-xl border border-gray-700">
              <div className="flex items-center gap-2 mb-2">
                <SparklesIcon className="h-4 w-4 text-orange-400" />
                <span className="text-sm font-medium text-white">Quick Start</span>
              </div>
              <p className="text-xs text-gray-400 mb-3">
                Get up and running in under 2 minutes
              </p>
              <button 
                onClick={() => setActiveSection('getting-started')}
                className="w-full bg-orange-600 hover:bg-orange-700 text-white text-xs py-2 px-3 rounded-lg transition-colors"
              >
                Start Building
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-black text-white overflow-auto">
          <div className="max-w-4xl mx-auto p-8">
            <motion.div
              key={activeSection}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {activeSection === 'getting-started' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">
                      Getting Started with RouKey
                    </h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      RouKey is an intelligent AI gateway that optimizes your LLM API usage through
                      advanced routing strategies. Get up and running in under 2 minutes.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-gradient-to-br from-orange-900/40 to-orange-800/40 p-6 rounded-xl border border-orange-500/30 backdrop-blur-sm">
                      <h3 className="text-lg font-semibold text-white mb-2 flex items-center gap-2">
                        <BoltIcon className="h-5 w-5 text-orange-400" />
                        Quick Start
                      </h3>
                      <p className="text-gray-300 mb-4">Get up and running with RouKey in minutes</p>
                      <button
                        onClick={() => setActiveSection('authentication')}
                        className="text-orange-400 hover:text-orange-300 font-medium flex items-center gap-1 transition-colors"
                      >
                        Start here <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="bg-gradient-to-br from-blue-900/40 to-blue-800/40 p-6 rounded-xl border border-blue-500/30 backdrop-blur-sm">
                      <h3 className="text-lg font-semibold text-white mb-2 flex items-center gap-2">
                        <CodeBracketIcon className="h-5 w-5 text-blue-400" />
                        API Reference
                      </h3>
                      <p className="text-gray-300 mb-4">Complete API documentation and examples</p>
                      <button
                        onClick={() => setActiveSection('api-reference')}
                        className="text-blue-400 hover:text-blue-300 font-medium flex items-center gap-1 transition-colors"
                      >
                        View API docs <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">What is RouKey?</h2>
                    <div className="prose prose-gray max-w-none">
                      <p className="text-gray-300 leading-relaxed text-lg">
                        RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers.
                        It automatically routes requests to the most appropriate model based on your configured strategies,
                        providing cost optimization, improved reliability, and enhanced performance.
                      </p>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Key Features</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700">
                        <BoltIcon className="h-8 w-8 text-orange-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-white text-lg mb-2">Intelligent Routing</h3>
                          <p className="text-gray-300">AI-powered request classification and optimal model selection</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700">
                        <CogIcon className="h-8 w-8 text-blue-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-white text-lg mb-2">Multiple Strategies</h3>
                          <p className="text-gray-300">Fallback, cost-optimized, role-based, and complexity routing</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700">
                        <KeyIcon className="h-8 w-8 text-green-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-white text-lg mb-2">Secure & Reliable</h3>
                          <p className="text-gray-300">Enterprise-grade security with automatic failover</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-4 p-6 bg-gray-800/50 rounded-xl border border-gray-700">
                        <SparklesIcon className="h-8 w-8 text-purple-400 flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-white text-lg mb-2">Cost Optimization</h3>
                          <p className="text-gray-300">Reduce costs by up to 60% with smart routing</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-800/30 p-6 rounded-xl border border-gray-700">
                    <h3 className="text-xl font-semibold text-white mb-4">Ready to get started?</h3>
                    <p className="text-gray-300 mb-4">
                      Follow our quick start guide to integrate RouKey into your application in minutes.
                    </p>
                    <button
                      onClick={() => setActiveSection('authentication')}
                      className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                    >
                      Get Started Now
                    </button>
                  </div>
                </div>
              )}

              {activeSection === 'authentication' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">Authentication</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Learn how to authenticate with RouKey using API keys.
                    </p>
                  </div>

                  <Alert type="info">
                    <strong>Important:</strong> RouKey uses the <code className="bg-blue-900/50 px-2 py-1 rounded text-blue-300">X-API-Key</code> header for authentication.
                    Never use <code className="bg-blue-900/50 px-2 py-1 rounded text-blue-300">Authorization</code> header format.
                  </Alert>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Getting Your API Key</h2>
                    <div className="space-y-4">
                      <p className="text-gray-300 text-lg">
                        To get started with RouKey, you'll need to create an API key from your dashboard:
                      </p>
                      <ol className="list-decimal list-inside space-y-3 text-gray-300 ml-4 text-lg">
                        <li>Sign up for a RouKey account at <a href="https://roukey.online" className="text-orange-400 hover:text-orange-300 underline">roukey.online</a></li>
                        <li>Navigate to your dashboard and create a configuration</li>
                        <li>Add your LLM provider API keys (OpenAI, Anthropic, etc.)</li>
                        <li>Generate a user API key for external access</li>
                        <li>Copy your API key (format: <code className="bg-gray-800 px-2 py-1 rounded text-gray-300">rk_live_...</code>)</li>
                      </ol>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Authentication Methods</h2>
                    <div className="space-y-8">
                      <div>
                        <h3 className="text-xl font-semibold text-white mb-4">Method 1: X-API-Key Header (Recommended)</h3>
                        <CodeBlock title="Using X-API-Key header" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'`}
                        </CodeBlock>
                      </div>

                      <div>
                        <h3 className="text-xl font-semibold text-white mb-4">Method 2: Bearer Token</h3>
                        <CodeBlock title="Using Authorization Bearer header" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer rk_live_your_api_key_here" \\
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'`}
                        </CodeBlock>
                      </div>
                    </div>
                  </div>

                  <Alert type="tip">
                    <strong>Best Practice:</strong> Always use the <code className="bg-green-900/50 px-2 py-1 rounded text-green-300">X-API-Key</code> header method
                    as it's the primary authentication method for RouKey and ensures maximum compatibility.
                  </Alert>
                </div>
              )}

              {activeSection === 'api-reference' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">API Reference</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Complete reference for the RouKey API endpoints and parameters.
                    </p>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Base URL</h2>
                    <CodeBlock title="Production Base URL">
{`https://roukey.online/api/external/v1`}
                    </CodeBlock>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Chat Completions</h2>
                    <p className="text-gray-300 mb-6 text-lg">
                      Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API.
                    </p>

                    <div className="bg-gray-800/50 p-6 rounded-xl mb-6 border border-gray-700">
                      <div className="flex items-center gap-3 mb-3">
                        <span className="bg-green-600 text-white px-3 py-1 rounded-lg text-sm font-medium">POST</span>
                        <code className="text-gray-100 text-lg">/chat/completions</code>
                      </div>
                      <p className="text-gray-400">
                        OpenAI-compatible endpoint with RouKey's intelligent routing capabilities
                      </p>
                    </div>

                    <h3 className="text-xl font-semibold text-white mb-4">Request Parameters</h3>
                    <div className="overflow-x-auto mb-8">
                      <table className="min-w-full bg-gray-800/50 border border-gray-700 rounded-xl">
                        <thead className="bg-gray-700/50">
                          <tr>
                            <th className="px-6 py-4 text-left text-sm font-medium text-white">Parameter</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-white">Type</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-white">Required</th>
                            <th className="px-6 py-4 text-left text-sm font-medium text-white">Description</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-700">
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-orange-400">messages</td>
                            <td className="px-6 py-4 text-sm text-gray-300">array</td>
                            <td className="px-6 py-4 text-sm text-green-400">Yes</td>
                            <td className="px-6 py-4 text-sm text-gray-300">Array of message objects</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-orange-400">stream</td>
                            <td className="px-6 py-4 text-sm text-gray-300">boolean</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-300">Enable streaming responses (recommended for multi-role tasks)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-orange-400">temperature</td>
                            <td className="px-6 py-4 text-sm text-gray-300">number</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-300">Sampling temperature (0-2)</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-orange-400">max_tokens</td>
                            <td className="px-6 py-4 text-sm text-gray-300">integer</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-300">Maximum tokens to generate</td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 text-sm font-mono text-orange-400">role</td>
                            <td className="px-6 py-4 text-sm text-gray-300">string</td>
                            <td className="px-6 py-4 text-sm text-gray-500">No</td>
                            <td className="px-6 py-4 text-sm text-gray-300">RouKey-specific role for routing (e.g., "coding", "writing")</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <h3 className="text-xl font-semibold text-white mb-4">Example Request</h3>
                    <CodeBlock title="Basic chat completion" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Explain quantum computing"}
  ],
  "stream": false,
  "temperature": 0.7,
  "max_tokens": 500
}`}
                    </CodeBlock>

                    <h3 className="text-xl font-semibold text-white mb-4 mt-8">Example with Role-Based Routing</h3>
                    <CodeBlock title="Role-based routing request" language="json">
{`{
  "messages": [
    {"role": "user", "content": "Write a Python function to sort a list"}
  ],
  "role": "coding",
  "stream": true,
  "max_tokens": 1000
}`}
                    </CodeBlock>

                    <Alert type="tip">
                      <strong>Streaming Recommended:</strong> For complex tasks that may involve multiple roles or require significant processing,
                      use <code className="bg-green-900/50 px-2 py-1 rounded text-green-300">stream: true</code> to avoid timeouts and get real-time responses.
                    </Alert>
                  </div>
                </div>
              )}

              {activeSection === 'examples' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">Examples</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Practical examples to get you started with RouKey in different programming languages.
                    </p>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">JavaScript/Node.js</h2>
                    <CodeBlock title="Basic chat completion with fetch" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Explain machine learning in simple terms' }
    ],
    stream: false,
    max_tokens: 500
  })
});

const data = await response.json();
console.log(data.choices[0].message.content);`}
                    </CodeBlock>

                    <CodeBlock title="Streaming response example" language="javascript">
{`const response = await fetch('https://roukey.online/api/external/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'rk_live_your_api_key_here'
  },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'Write a detailed explanation of quantum computing' }
    ],
    stream: true,
    max_tokens: 1000
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;

  const chunk = decoder.decode(value);
  const lines = chunk.split('\\n');

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') return;

      try {
        const parsed = JSON.parse(data);
        const content = parsed.choices[0]?.delta?.content;
        if (content) {
          process.stdout.write(content);
        }
      } catch (e) {
        // Skip invalid JSON
      }
    }
  }
}`}
                    </CodeBlock>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">Python</h2>
                    <CodeBlock title="Basic chat completion with requests" language="python">
{`import requests
import json

response = requests.post(
    'https://roukey.online/api/external/v1/chat/completions',
    headers={
        'Content-Type': 'application/json',
        'X-API-Key': 'rk_live_your_api_key_here'
    },
    json={
        'messages': [
            {'role': 'user', 'content': 'Explain machine learning in simple terms'}
        ],
        'stream': False,
        'max_tokens': 500
    }
)

data = response.json()
print(data['choices'][0]['message']['content'])`}
                    </CodeBlock>

                    <CodeBlock title="Streaming response with requests" language="python">
{`import requests
import json

response = requests.post(
    'https://roukey.online/api/external/v1/chat/completions',
    headers={
        'Content-Type': 'application/json',
        'X-API-Key': 'rk_live_your_api_key_here'
    },
    json={
        'messages': [
            {'role': 'user', 'content': 'Write a detailed explanation of quantum computing'}
        ],
        'stream': True,
        'max_tokens': 1000
    },
    stream=True
)

for line in response.iter_lines():
    if line:
        line = line.decode('utf-8')
        if line.startswith('data: '):
            data = line[6:]
            if data == '[DONE]':
                break
            try:
                parsed = json.loads(data)
                content = parsed['choices'][0]['delta'].get('content', '')
                if content:
                    print(content, end='', flush=True)
            except json.JSONDecodeError:
                continue`}
                    </CodeBlock>
                  </div>

                  <div>
                    <h2 className="text-3xl font-bold text-white mb-6">cURL</h2>
                    <CodeBlock title="Basic request with cURL" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "stream": false,
    "max_tokens": 150
  }'`}
                    </CodeBlock>

                    <CodeBlock title="Role-based routing with cURL" language="bash">
{`curl -X POST "https://roukey.online/api/external/v1/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: rk_live_your_api_key_here" \\
  -d '{
    "messages": [
      {"role": "user", "content": "Write a Python function to calculate fibonacci numbers"}
    ],
    "role": "coding",
    "stream": true,
    "max_tokens": 500
  }'`}
                    </CodeBlock>
                  </div>

                  <Alert type="info">
                    <strong>Need more examples?</strong> Check out our GitHub repository for complete example applications
                    and integration guides for popular frameworks like React, Vue, and Express.js.
                  </Alert>
                </div>
              )}

              {/* Add placeholder content for other sections */}
              {activeSection === 'routing-strategies' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">Routing Strategies</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      RouKey offers multiple intelligent routing strategies to optimize your LLM usage.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center">
                    <h3 className="text-xl font-semibold text-white mb-4">Coming Soon</h3>
                    <p className="text-gray-300">Detailed routing strategies documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {activeSection === 'configuration' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">Configuration</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Learn how to configure RouKey for optimal performance.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center">
                    <h3 className="text-xl font-semibold text-white mb-4">Coming Soon</h3>
                    <p className="text-gray-300">Configuration documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {activeSection === 'sdks' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">SDKs & Libraries</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Official SDKs and community libraries for RouKey.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center">
                    <h3 className="text-xl font-semibold text-white mb-4">Coming Soon</h3>
                    <p className="text-gray-300">SDK documentation is being prepared.</p>
                  </div>
                </div>
              )}

              {activeSection === 'limits' && (
                <div className="space-y-8">
                  <div>
                    <h1 className="text-5xl font-bold text-white mb-6">Limits & Pricing</h1>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                      Understanding RouKey's usage limits and pricing structure.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center">
                    <h3 className="text-xl font-semibold text-white mb-4">Coming Soon</h3>
                    <p className="text-gray-300">Limits and pricing documentation is being prepared.</p>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
