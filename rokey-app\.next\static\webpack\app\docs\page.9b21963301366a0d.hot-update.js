"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/docs/page",{

/***/ "(app-pages-browser)/./src/app/docs/page.tsx":
/*!*******************************!*\
  !*** ./src/app/docs/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CheckIcon,ChevronRightIcon,ClipboardDocumentIcon,CodeBracketIcon,CogIcon,DocumentTextIcon,ExclamationTriangleIcon,InformationCircleIcon,KeyIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction CodeBlock(param) {\n    let { children, language = 'javascript', title } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopy = async ()=>{\n        await navigator.clipboard.writeText(children);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    // Enhanced syntax highlighting for different languages\n    const highlightSyntax = (code, lang)=>{\n        const lines = code.split('\\n');\n        return lines.map((line, index)=>{\n            let highlightedLine = line;\n            if (lang === 'javascript' || lang === 'typescript') {\n                // Keywords\n                highlightedLine = highlightedLine.replace(/\\b(const|let|var|function|async|await|import|export|from|return|if|else|for|while|try|catch|throw|new)\\b/g, '<span class=\"text-purple-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n                // Comments\n                highlightedLine = highlightedLine.replace(/(\\/\\/.*$)/g, '<span class=\"text-gray-500\">$1</span>');\n                // Numbers\n                highlightedLine = highlightedLine.replace(/\\b(\\d+\\.?\\d*)\\b/g, '<span class=\"text-yellow-400\">$1</span>');\n            } else if (lang === 'python') {\n                // Python keywords\n                highlightedLine = highlightedLine.replace(/\\b(import|from|def|class|if|else|elif|for|while|try|except|with|as|return|yield|lambda|and|or|not|in|is|None|True|False)\\b/g, '<span class=\"text-purple-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n                // Comments\n                highlightedLine = highlightedLine.replace(/(#.*$)/g, '<span class=\"text-gray-500\">$1</span>');\n            } else if (lang === 'bash' || lang === 'shell') {\n                // Bash commands\n                highlightedLine = highlightedLine.replace(/\\b(curl|echo|export|cd|ls|mkdir|rm|cp|mv|grep|awk|sed)\\b/g, '<span class=\"text-blue-400\">$1</span>');\n                // Flags\n                highlightedLine = highlightedLine.replace(/(-[a-zA-Z]+)/g, '<span class=\"text-yellow-400\">$1</span>');\n                // Strings\n                highlightedLine = highlightedLine.replace(/([\"'`])((?:\\\\.|(?!\\1)[^\\\\])*?)\\1/g, '<span class=\"text-green-400\">$1$2$1</span>');\n            } else if (lang === 'json') {\n                // JSON keys\n                highlightedLine = highlightedLine.replace(/\"([^\"]+)\":/g, '<span class=\"text-blue-400\">\"$1\"</span>:');\n                // JSON strings\n                highlightedLine = highlightedLine.replace(/:\\s*\"([^\"]*)\"/g, ': <span class=\"text-green-400\">\"$1\"</span>');\n                // JSON numbers\n                highlightedLine = highlightedLine.replace(/:\\s*(\\d+\\.?\\d*)/g, ': <span class=\"text-yellow-400\">$1</span>');\n                // JSON booleans\n                highlightedLine = highlightedLine.replace(/:\\s*(true|false|null)/g, ': <span class=\"text-purple-400\">$1</span>');\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"table-row\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"table-cell text-gray-500 text-right pr-4 select-none w-8\",\n                        children: index + 1\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"table-cell text-gray-100\",\n                        dangerouslySetInnerHTML: {\n                            __html: highlightedLine || ' '\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative bg-gray-900 rounded-xl overflow-hidden border border-gray-700 shadow-2xl\",\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 px-4 py-3 text-sm text-gray-300 border-b border-gray-700 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                        children: language\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"p-4 overflow-x-auto text-sm font-mono leading-relaxed\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"table w-full\",\n                            children: highlightSyntax(children, language || 'javascript')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"absolute top-3 right-3 p-2 bg-gray-800/80 hover:bg-gray-700 rounded-lg transition-all duration-200 backdrop-blur-sm border border-gray-600\",\n                        title: \"Copy to clipboard\",\n                        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4 text-green-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeBlock, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = CodeBlock;\nfunction Alert(param) {\n    let { type, children } = param;\n    const styles = {\n        info: 'bg-blue-50 border-blue-200 text-blue-800',\n        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n        tip: 'bg-green-50 border-green-200 text-green-800'\n    };\n    const icons = {\n        info: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        warning: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        tip: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    };\n    const Icon = icons[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-lg p-4 \".concat(styles[type]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-5 w-5 flex-shrink-0 mt-0.5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Alert;\nfunction DocsPage() {\n    _s1();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('getting-started');\n    const sections = [\n        {\n            id: 'getting-started',\n            title: 'Getting Started',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'authentication',\n            title: 'Authentication',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'api-reference',\n            title: 'API Reference',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 'routing-strategies',\n            title: 'Routing Strategies',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            id: 'configuration',\n            title: 'Configuration',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: 'examples',\n            title: 'Examples',\n            icon: _barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:w-64 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Documentation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"space-y-2\",\n                                        children: sections.map((section)=>{\n                                            const Icon = section.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveSection(section.id),\n                                                className: \"w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors \".concat(activeSection === section.id ? 'bg-orange-100 text-orange-700 border border-orange-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    section.title\n                                                ]\n                                            }, section.id, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 max-w-4xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: [\n                                    activeSection === 'getting-started' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                        children: \"RouKey Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8\",\n                                                        children: \"Welcome to RouKey - the intelligent AI gateway that optimizes your LLM API usage through advanced routing strategies.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-orange-50 to-orange-100 p-6 rounded-xl border border-orange-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                                children: \"\\uD83D\\uDE80 Quick Start\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: \"Get up and running with RouKey in minutes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setActiveSection('authentication'),\n                                                                className: \"text-orange-600 hover:text-orange-700 font-medium flex items-center gap-1\",\n                                                                children: [\n                                                                    \"Start here \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                                children: \"\\uD83D\\uDCDA API Reference\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-4\",\n                                                                children: \"Complete API documentation and examples\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setActiveSection('api-reference'),\n                                                                className: \"text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1\",\n                                                                children: [\n                                                                    \"View API docs \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 39\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"What is RouKey?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"prose prose-gray max-w-none\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 leading-relaxed\",\n                                                            children: \"RouKey is an intelligent AI gateway that sits between your application and multiple LLM providers. It automatically routes requests to the most appropriate model based on your configured strategies, providing cost optimization, improved reliability, and enhanced performance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Key Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-3 p-4 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-orange-500 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900\",\n                                                                                children: \"Intelligent Routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"AI-powered request classification and optimal model selection\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-3 p-4 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-blue-500 flex-shrink-0 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900\",\n                                                                                children: \"Multiple Strategies\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: \"Fallback, cost-optimized, role-based, and complexity routing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 307,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'authentication' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Authentication\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8\",\n                                                        children: \"Learn how to authenticate with RouKey using API keys.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Important:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" RouKey uses the \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-blue-100 px-1 rounded\",\n                                                        children: \"X-API-Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \" header for authentication. Never use \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-blue-100 px-1 rounded\",\n                                                        children: \"Authorization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 31\n                                                    }, this),\n                                                    \" header format.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Getting Your API Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"To get started with RouKey, you'll need to create an API key from your dashboard:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                className: \"list-decimal list-inside space-y-2 text-gray-600 ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Sign up for a RouKey account at \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                href: \"https://roukey.online\",\n                                                                                className: \"text-orange-600 hover:text-orange-700\",\n                                                                                children: \"roukey.online\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 336,\n                                                                                columnNumber: 61\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Navigate to your dashboard and create a configuration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Add your LLM provider API keys (OpenAI, Anthropic, etc.)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Generate a user API key for external access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"Copy your API key (format: \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                className: \"bg-gray-100 px-1 rounded\",\n                                                                                children: \"rk_live_...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 340,\n                                                                                columnNumber: 56\n                                                                            }, this),\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Authentication Methods\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                                        children: \"Method 1: X-API-Key Header (Recommended)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Using X-API-Key header\",\n                                                                        children: 'curl -X POST \"https://your-domain.com/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],\\n    \"stream\": false\\n  }\\''\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                                        children: \"Method 2: Bearer Token\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Using Authorization Bearer header\",\n                                                                        children: 'curl -X POST \"https://your-domain.com/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"Authorization: Bearer rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],\\n    \"stream\": false\\n  }\\''\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"tip\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best Practice:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Always use the \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-green-100 px-1 rounded\",\n                                                        children: \"X-API-Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 68\n                                                    }, this),\n                                                    \" header method as it's the primary authentication method for RouKey and ensures maximum compatibility.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'api-reference' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                        children: \"API Reference\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8\",\n                                                        children: \"Complete reference for the RouKey API endpoints and parameters.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Base URL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        children: \"https://your-domain.com/api/external/v1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Chat Completions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-4\",\n                                                        children: \"Create a chat completion using RouKey's intelligent routing. This endpoint is fully compatible with OpenAI's API.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-4 rounded-lg mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium\",\n                                                                    children: \"POST\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                    className: \"text-gray-800\",\n                                                                    children: \"/chat/completions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                        children: \"Request Body\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-x-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                className: \"min-w-full bg-white border border-gray-200 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                        className: \"bg-gray-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-4 py-3 text-left text-sm font-medium text-gray-900\",\n                                                                                    children: \"Parameter\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 418,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-4 py-3 text-left text-sm font-medium text-gray-900\",\n                                                                                    children: \"Type\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 419,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-4 py-3 text-left text-sm font-medium text-gray-900\",\n                                                                                    children: \"Required\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 420,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                    className: \"px-4 py-3 text-left text-sm font-medium text-gray-900\",\n                                                                                    children: \"Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                    lineNumber: 421,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 417,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                        className: \"divide-y divide-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm font-mono text-gray-900\",\n                                                                                        children: \"messages\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 426,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                        children: \"array\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 427,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-green-600\",\n                                                                                        children: \"Yes\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 428,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                        children: \"Array of message objects\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 429,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm font-mono text-gray-900\",\n                                                                                        children: \"stream\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 432,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                        children: \"boolean\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 433,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                        children: \"No\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 434,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                        children: \"Enable streaming responses (recommended for multi-role tasks)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 435,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm font-mono text-gray-900\",\n                                                                                        children: \"temperature\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 438,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                        children: \"number\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 439,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                        children: \"No\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 440,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                        children: \"Sampling temperature (0-2)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 441,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 437,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm font-mono text-gray-900\",\n                                                                                        children: \"max_tokens\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 444,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                        children: \"integer\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 445,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                        children: \"No\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 446,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                        children: \"Maximum tokens to generate\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 447,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 443,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm font-mono text-gray-900\",\n                                                                                        children: \"role\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 450,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                        children: \"string\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 451,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                                        children: \"No\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 452,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                        className: \"px-4 py-3 text-sm text-gray-600\",\n                                                                                        children: 'RouKey-specific role for routing (e.g., \"coding\", \"writing\")'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 453,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 449,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-3 mt-6\",\n                                                        children: \"Example Request\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion\",\n                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Explain quantum computing\"}\\n  ],\\n  \"stream\": false,\\n  \"temperature\": 0.7,\\n  \"max_tokens\": 500\\n}'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-3 mt-6\",\n                                                        children: \"Example with Role-Based Routing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Role-based routing request\",\n                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Write a Python function to sort a list\"}\\n  ],\\n  \"role\": \"coding\",\\n  \"stream\": true,\\n  \"max_tokens\": 1000\\n}'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                        type: \"tip\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Streaming Recommended:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" For complex tasks that may involve multiple roles or require significant processing, use \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                className: \"bg-green-100 px-1 rounded\",\n                                                                children: \"stream: true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \" to avoid timeouts and get real-time responses.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'routing-strategies' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Routing Strategies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8\",\n                                                        children: \"RouKey offers multiple intelligent routing strategies to optimize your LLM usage.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-orange-50 to-orange-100 p-6 rounded-xl border border-orange-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 504,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Intelligent Role Routing\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-3\",\n                                                                children: \"AI-powered classification routes requests to specialized models based on detected roles.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Best for:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Applications with diverse use cases (coding, writing, analysis)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Complexity-Based Routing\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-3\",\n                                                                children: \"Routes based on prompt complexity (1-5) to optimize cost and performance.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Best for:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Cost optimization and performance tuning\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl border border-green-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 530,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Strict Fallback\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-3\",\n                                                                children: \"Ordered sequence of API keys with guaranteed fallback behavior.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Best for:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 537,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Predictable routing with manual priority control\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_CheckIcon_ChevronRightIcon_ClipboardDocumentIcon_CodeBracketIcon_CogIcon_DocumentTextIcon_ExclamationTriangleIcon_InformationCircleIcon_KeyIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-purple-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Cost Optimized\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 mb-3\",\n                                                                children: \"Smart routing based on cost profiles and usage patterns.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Best for:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Minimizing costs while maintaining quality\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Configuring Routing Strategies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-4\",\n                                                        children: \"Routing strategies are configured in your RouKey dashboard. Here's how each strategy works:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border border-gray-200 rounded-lg p-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                                        children: \"Intelligent Role Routing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 mb-4\",\n                                                                        children: \"RouKey's AI classifier analyzes your prompt and routes to the most appropriate model based on detected roles.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                                children: \"Supported Roles:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 568,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-2 md:grid-cols-3 gap-2 text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"• coding\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 570,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"• writing\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 571,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"• analysis\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 572,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"• general_chat\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 573,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"• creative\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 574,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"• technical\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 575,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 569,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                                        title: \"Example with role specification\",\n                                                                        children: '{\\n  \"messages\": [\\n    {\"role\": \"user\", \"content\": \"Debug this Python code\"}\\n  ],\\n  \"role\": \"coding\",\\n  \"stream\": true\\n}'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border border-gray-200 rounded-lg p-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                                        children: \"Complexity-Based Routing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 mb-4\",\n                                                                        children: \"Automatically classifies prompt complexity (1-5) and routes to appropriate models.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                                children: \"Complexity Levels:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 595,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1 text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Level 1:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 597,\n                                                                                                columnNumber: 34\n                                                                                            }, this),\n                                                                                            \" Simple questions, basic tasks\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 597,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Level 2:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 598,\n                                                                                                columnNumber: 34\n                                                                                            }, this),\n                                                                                            \" Moderate complexity, explanations\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 598,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Level 3:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 599,\n                                                                                                columnNumber: 34\n                                                                                            }, this),\n                                                                                            \" Complex analysis, detailed responses\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 599,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Level 4:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 600,\n                                                                                                columnNumber: 34\n                                                                                            }, this),\n                                                                                            \" Advanced reasoning, multi-step tasks\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 600,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                                children: \"Level 5:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                                lineNumber: 601,\n                                                                                                columnNumber: 34\n                                                                                            }, this),\n                                                                                            \" Highly complex, research-level tasks\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                        lineNumber: 601,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 596,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'configuration' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8\",\n                                                        children: \"Learn how to configure RouKey for optimal performance.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Setting Up Your Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"A configuration in RouKey represents a complete setup with API keys, routing strategy, and settings.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                className: \"list-decimal list-inside space-y-2 text-gray-600 ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Create a new configuration in your dashboard\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 626,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Add your LLM provider API keys (OpenAI, Anthropic, Google, etc.)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Assign roles to specific API keys (for role-based routing)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Choose your routing strategy\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Generate a user API key for external access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"API Key Management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-6 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-gray-900 mb-3\",\n                                                                children: \"Supported Providers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 641,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"OpenAI\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 642,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 640,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 645,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"Anthropic\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 646,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 644,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 649,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"Google AI\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 650,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 653,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"Cohere\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 654,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 652,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 657,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"Mistral\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 658,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 656,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-700\",\n                                                                                children: \"And more...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                                lineNumber: 662,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"warning\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Security Note:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Your provider API keys are securely stored and never exposed. RouKey acts as a secure proxy, using your keys only for authorized requests.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeSection === 'examples' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Examples\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-gray-600 mb-8\",\n                                                        children: \"Practical examples to get you started with RouKey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"JavaScript/Node.js\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Basic chat completion with fetch\",\n                                                        children: \"const response = await fetch('https://your-domain.com/api/external/v1/chat/completions', {\\n  method: 'POST',\\n  headers: {\\n    'Content-Type': 'application/json',\\n    'X-API-Key': 'rk_live_your_api_key_here'\\n  },\\n  body: JSON.stringify({\\n    messages: [\\n      { role: 'user', content: 'Explain machine learning in simple terms' }\\n    ],\\n    stream: false,\\n    max_tokens: 500\\n  })\\n});\\n\\nconst data = await response.json();\\nconsole.log(data.choices[0].message.content);\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Streaming response example\",\n                                                        children: \"const response = await fetch('https://your-domain.com/api/external/v1/chat/completions', {\\n  method: 'POST',\\n  headers: {\\n    'Content-Type': 'application/json',\\n    'X-API-Key': 'rk_live_your_api_key_here'\\n  },\\n  body: JSON.stringify({\\n    messages: [\\n      { role: 'user', content: 'Write a detailed analysis of renewable energy' }\\n    ],\\n    stream: true,\\n    max_tokens: 2000\\n  })\\n});\\n\\nconst reader = response.body.getReader();\\nconst decoder = new TextDecoder();\\n\\nwhile (true) {\\n  const { done, value } = await reader.read();\\n  if (done) break;\\n\\n  const chunk = decoder.decode(value);\\n  const lines = chunk.split('\\\\n');\\n\\n  for (const line of lines) {\\n    if (line.startsWith('data: ')) {\\n      const data = line.slice(6);\\n      if (data === '[DONE]') return;\\n\\n      try {\\n        const parsed = JSON.parse(data);\\n        const content = parsed.choices[0]?.delta?.content;\\n        if (content) {\\n          process.stdout.write(content);\\n        }\\n      } catch (e) {\\n        // Skip invalid JSON\\n      }\\n    }\\n  }\\n}\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"Python\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Using requests library\",\n                                                        children: 'import requests\\nimport json\\n\\nurl = \"https://your-domain.com/api/external/v1/chat/completions\"\\nheaders = {\\n    \"Content-Type\": \"application/json\",\\n    \"X-API-Key\": \"rk_live_your_api_key_here\"\\n}\\n\\ndata = {\\n    \"messages\": [\\n        {\"role\": \"user\", \"content\": \"Generate a Python function to calculate fibonacci\"}\\n    ],\\n    \"role\": \"coding\",\\n    \"stream\": False,\\n    \"max_tokens\": 1000\\n}\\n\\nresponse = requests.post(url, headers=headers, json=data)\\nresult = response.json()\\n\\nprint(result[\"choices\"][0][\"message\"][\"content\"])'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Streaming with Python\",\n                                                        children: 'import requests\\nimport json\\n\\nurl = \"https://your-domain.com/api/external/v1/chat/completions\"\\nheaders = {\\n    \"Content-Type\": \"application/json\",\\n    \"X-API-Key\": \"rk_live_your_api_key_here\"\\n}\\n\\ndata = {\\n    \"messages\": [\\n        {\"role\": \"user\", \"content\": \"Explain quantum computing with examples\"}\\n    ],\\n    \"stream\": True,\\n    \"max_tokens\": 2000\\n}\\n\\nresponse = requests.post(url, headers=headers, json=data, stream=True)\\n\\nfor line in response.iter_lines():\\n    if line:\\n        line = line.decode(\\'utf-8\\')\\n        if line.startswith(\\'data: \\'):\\n            data_str = line[6:]\\n            if data_str == \\'[DONE]\\':\\n                break\\n            try:\\n                data_obj = json.loads(data_str)\\n                content = data_obj.get(\\'choices\\', [{}])[0].get(\\'delta\\', {}).get(\\'content\\', \\'\\')\\n                if content:\\n                    print(content, end=\\'\\', flush=True)\\n            except json.JSONDecodeError:\\n                continue'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                        children: \"cURL Examples\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Simple request\",\n                                                        children: 'curl -X POST \"https://your-domain.com/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Hello, world!\"}],\\n    \"stream\": false,\\n    \"max_tokens\": 100\\n  }\\''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CodeBlock, {\n                                                        title: \"Role-based routing\",\n                                                        children: 'curl -X POST \"https://your-domain.com/api/external/v1/chat/completions\" \\\\\\n  -H \"Content-Type: application/json\" \\\\\\n  -H \"X-API-Key: rk_live_your_api_key_here\" \\\\\\n  -d \\'{\\n    \"messages\": [{\"role\": \"user\", \"content\": \"Write a creative story about space\"}],\\n    \"role\": \"creative\",\\n    \"stream\": true,\\n    \"temperature\": 0.9,\\n    \"max_tokens\": 1500\\n  }\\''\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 829,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                                type: \"tip\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Pro Tip:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" For complex tasks involving multiple roles or long responses, always use\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-green-100 px-1 rounded\",\n                                                        children: \"stream: true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" to avoid timeouts and get real-time feedback.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, activeSection, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 854,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s1(DocsPage, \"x6z6yFCb1AOn1CFzZeI0YdLu3rM=\");\n_c2 = DocsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CodeBlock\");\n$RefreshReg$(_c1, \"Alert\");\n$RefreshReg$(_c2, \"DocsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/docs/page.tsx\n"));

/***/ })

});